#!/usr/bin/env python3
"""
MCP Knowledge Server 启动脚本
"""

import subprocess
import sys
import os

def main():
    """启动 MCP Knowledge Server"""
    print("=" * 50)
    print("启动 MCP Knowledge Server...")
    print("=" * 50)
    
    # 检查依赖
    try:
        import fastapi
        import uvicorn
        print("✓ 依赖检查通过")
    except ImportError as e:
        print(f"✗ 缺少依赖: {e}")
        print("请运行: pip install -r requirements.txt")
        return 1
    
    # 启动服务器
    try:
        cmd = [sys.executable, "http_server.py", "--port", "8085", "--host", "0.0.0.0"]
        print(f"执行命令: {' '.join(cmd)}")
        print("服务器将在 http://localhost:8085/mcp 启动")
        print("按 Ctrl+C 停止服务器")
        print("-" * 50)
        
        subprocess.run(cmd, cwd=os.path.dirname(os.path.abspath(__file__)))
        
    except KeyboardInterrupt:
        print("\n服务器已停止")
        return 0
    except Exception as e:
        print(f"启动失败: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
