# MCP Knowledge Server

基于 Model Context Protocol (MCP) 的知识库服务器，提供股市数据查询功能。

## 功能特性

- 符合 MCP 2024-11-05 协议规范
- Streamable HTTP 传输
- 股市数据知识库查询
- 向量数据库支持 (Milvus)

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 启动服务器

```bash
python start.py
```

或者直接运行：

```bash
python http_server.py --port 8085
```

### 3. 服务器信息

- **MCP 端点**: `http://localhost:8085/mcp`
- **健康检查**: `http://localhost:8085/health`
- **协议版本**: 2024-11-05 (默认)

## MCP 客户端配置

在 Cherry Studio 或其他 MCP 客户端中配置：

- **类型**: 可流式传输的 HTTP (streamableHttp)
- **URL**: `http://localhost:8085/mcp`
- **请求头**: 
  - `Content-Type: application/json`
  - `Authorization: Bearer token` (可选)

## 可用工具

### get_knowledge_data

获取知识数据，基于查询文本搜索相关文档。

**参数**:
- `query` (string): 查询文本，用于搜索相关文档

**示例**:
```json
{
  "name": "get_knowledge_data",
  "arguments": {
    "query": "股票市场趋势"
  }
}
```

## 项目结构

```
rag_mcp/
├── http_server.py      # MCP HTTP 服务器
├── knowledge.py        # 知识库管理
├── logger.py          # 日志工具
├── milvus_lite.py     # Milvus 数据库
├── milvus_manger.py   # 数据库管理器
├── config.json        # 配置文件
├── requirements.txt   # 依赖列表
├── start.py          # 启动脚本
└── README.md         # 说明文档
```

## 开发说明

- 基于 FastAPI 框架
- 使用 Milvus 向量数据库
- 支持会话管理
- 完整的错误处理和日志记录

## 故障排除

1. **端口被占用**: 修改 `--port` 参数
2. **依赖缺失**: 运行 `pip install -r requirements.txt`
3. **数据库连接**: 检查 Milvus 配置

## 许可证

MIT License
